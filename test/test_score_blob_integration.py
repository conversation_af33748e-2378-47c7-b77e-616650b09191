"""
Integration tests for score.py with Azure Blob Storage component downloader.

This module tests the integration between the scoring service and the blob downloader
functionality, ensuring proper initialization modes and fallback behavior.
"""

import os
from unittest.mock import Mock, patch

from autolodge import score


class TestScoreBlobIntegration:
    """Test cases for score.py integration with blob downloader."""

    def test_blob_downloader_import_success(self):
        """Test that blob downloader imports are successful."""
        # This test verifies that the imports in score.py work correctly
        assert hasattr(score, 'BLOB_DOWNLOADER_AVAILABLE')
        assert score.BLOB_DOWNLOADER_AVAILABLE is True

    @patch('autolodge.score.is_blob_download_enabled')
    @patch('autolodge.score.download_components_from_blob_storage')
    @patch('autolodge.score.nltk')
    def test_initialize_blob_storage_mode_success(self, mock_nltk, mock_download, mock_enabled):
        """Test successful initialization in blob storage mode."""
        # Setup mocks
        mock_enabled.return_value = True
        mock_download.return_value = True
        mock_nltk.download.return_value = None

        # Create resource manager
        config = score.Config()
        manager = score.ModelResourceManager(config)

        # Mock the model loading and preprocessing components
        with patch.object(manager, '_load_model_from_path') as mock_load_model:
            with patch.object(manager, '_load_preprocessing_components') as mock_load_components:
                mock_load_model.return_value = Mock()
                mock_load_components.return_value = (Mock(), Mock(), set(), [], Mock())

                # Test initialization
                manager.initialize()

                # Verify blob storage mode was used
                mock_enabled.assert_called_once()
                mock_download.assert_called_once()
                mock_load_model.assert_called_once()
                mock_load_components.assert_called_once()
                assert manager.is_initialized

    @patch('autolodge.score.is_blob_download_enabled')
    @patch('autolodge.score.download_components_from_blob_storage')
    @patch('autolodge.score.is_azure_ml_enabled')
    @patch('autolodge.score.nltk')
    def test_initialize_blob_storage_fallback_to_azure_ml(
        self, mock_nltk, mock_azure_ml_enabled, mock_download, mock_blob_enabled
    ):
        """Test fallback from blob storage to Azure ML mode."""
        # Setup mocks
        mock_blob_enabled.return_value = True
        mock_download.return_value = False  # Blob storage fails
        mock_azure_ml_enabled.return_value = True
        mock_nltk.download.return_value = None

        # Create resource manager
        config = score.Config()
        manager = score.ModelResourceManager(config)

        # Mock Azure ML components
        with patch('autolodge.score.get_azure_ml_model_path') as mock_get_model:
            with patch.object(manager, '_load_model_from_path') as mock_load_model:
                with patch.object(manager, '_load_preprocessing_components') as mock_load_components:
                    mock_get_model.return_value = ('/path/to/model', 'v1.0')
                    mock_load_model.return_value = Mock()
                    mock_load_components.return_value = (Mock(), Mock(), set(), [], Mock())

                    # Test initialization
                    manager.initialize()

                    # Verify fallback occurred
                    mock_blob_enabled.assert_called()
                    mock_download.assert_called_once()
                    mock_azure_ml_enabled.assert_called()
                    mock_get_model.assert_called_once()
                    assert manager.is_initialized

    @patch('autolodge.score.is_blob_download_enabled')
    @patch('autolodge.score.download_components_from_blob_storage')
    @patch('autolodge.score.is_azure_ml_enabled')
    @patch('autolodge.score.nltk')
    def test_initialize_blob_storage_fallback_to_local(
        self, mock_nltk, mock_azure_ml_enabled, mock_download, mock_blob_enabled
    ):
        """Test fallback from blob storage to local mode."""
        # Setup mocks
        mock_blob_enabled.return_value = True
        mock_download.return_value = False  # Blob storage fails
        mock_azure_ml_enabled.return_value = False  # Azure ML not available
        mock_nltk.download.return_value = None

        # Create resource manager
        config = score.Config()
        manager = score.ModelResourceManager(config)

        # Mock local mode components
        with patch.object(manager, '_load_model_from_path') as mock_load_model:
            with patch.object(manager, '_load_preprocessing_components') as mock_load_components:
                mock_load_model.return_value = Mock()
                mock_load_components.return_value = (Mock(), Mock(), set(), [], Mock())

                # Test initialization
                manager.initialize()

                # Verify fallback to local mode occurred
                mock_blob_enabled.assert_called()
                mock_download.assert_called_once()
                mock_azure_ml_enabled.assert_called()
                assert manager.is_initialized

    @patch('autolodge.score.is_blob_download_enabled')
    @patch('autolodge.score.is_azure_ml_enabled')
    @patch('autolodge.score.nltk')
    def test_initialize_priority_order(self, mock_nltk, mock_azure_ml_enabled, mock_blob_enabled):
        """Test that blob storage has priority over Azure ML mode."""
        # Setup mocks - both blob storage and Azure ML enabled
        mock_blob_enabled.return_value = True
        mock_azure_ml_enabled.return_value = True
        mock_nltk.download.return_value = None

        # Create resource manager
        config = score.Config()
        manager = score.ModelResourceManager(config)

        # Mock blob storage download
        with patch('autolodge.score.download_components_from_blob_storage') as mock_download:
            with patch.object(manager, '_load_model_from_path') as mock_load_model:
                with patch.object(manager, '_load_preprocessing_components') as mock_load_components:
                    mock_download.return_value = True
                    mock_load_model.return_value = Mock()
                    mock_load_components.return_value = (Mock(), Mock(), set(), [], Mock())

                    # Test initialization
                    manager.initialize()

                    # Verify blob storage mode was chosen (priority)
                    mock_blob_enabled.assert_called_once()
                    mock_download.assert_called_once()
                    # Azure ML should not be called since blob storage succeeded
                    assert manager.is_initialized

    @patch('autolodge.score.is_blob_download_enabled')
    def test_initialize_blob_storage_disabled(self, mock_blob_enabled):
        """Test initialization when blob storage is disabled."""
        # Setup mocks
        mock_blob_enabled.return_value = False

        # Create resource manager
        config = score.Config()
        manager = score.ModelResourceManager(config)

        # Mock Azure ML and local mode
        with patch('autolodge.score.is_azure_ml_enabled') as mock_azure_ml_enabled:
            with patch('autolodge.score.nltk') as mock_nltk:
                mock_azure_ml_enabled.return_value = False
                mock_nltk.download.return_value = None

                with patch.object(manager, '_load_model_from_path') as mock_load_model:
                    with patch.object(manager, '_load_preprocessing_components') as mock_load_components:
                        mock_load_model.return_value = Mock()
                        mock_load_components.return_value = (Mock(), Mock(), set(), [], Mock())

                        # Test initialization
                        manager.initialize()

                        # Verify blob storage was checked but not used
                        assert mock_blob_enabled.call_count >= 1
                        mock_azure_ml_enabled.assert_called()
                        assert manager.is_initialized

    def test_blob_downloader_availability_flag(self):
        """Test that BLOB_DOWNLOADER_AVAILABLE flag is set correctly."""
        # This test verifies the import logic in score.py
        assert hasattr(score, 'BLOB_DOWNLOADER_AVAILABLE')
        # Since we have the blob_downloader module, it should be True
        assert score.BLOB_DOWNLOADER_AVAILABLE is True

    @patch('autolodge.score.is_blob_download_enabled')
    @patch('autolodge.score.download_components_from_blob_storage')
    def test_blob_storage_mode_error_handling(self, mock_download, mock_enabled):
        """Test error handling in blob storage mode."""
        # Setup mocks
        mock_enabled.return_value = True
        mock_download.side_effect = Exception('Blob storage error')

        # Create resource manager
        config = score.Config()
        manager = score.ModelResourceManager(config)

        # Mock fallback to local mode
        with patch('autolodge.score.is_azure_ml_enabled') as mock_azure_ml_enabled:
            with patch('autolodge.score.nltk') as mock_nltk:
                mock_azure_ml_enabled.return_value = False
                mock_nltk.download.return_value = None

                with patch.object(manager, '_load_model_from_path') as mock_load_model:
                    with patch.object(manager, '_load_preprocessing_components') as mock_load_components:
                        mock_load_model.return_value = Mock()
                        mock_load_components.return_value = (Mock(), Mock(), set(), [], Mock())

                        # Test initialization - should handle error and fallback
                        manager.initialize()

                        # Verify error was handled and fallback occurred
                        mock_enabled.assert_called()
                        mock_download.assert_called_once()
                        assert manager.is_initialized


class TestEnvironmentVariableIntegration:
    """Test cases for environment variable integration."""

    def test_blob_download_environment_variables(self):
        """Test that blob download respects environment variables."""
        # Test with blob download disabled
        env_vars = {
            'AZURE_COMPONENTS_DOWNLOAD_ENABLED': 'false',
        }

        with patch.dict(os.environ, env_vars, clear=True):
            from autolodge.blob_downloader import is_blob_download_enabled

            assert is_blob_download_enabled() is False

        # Test with blob download enabled but no connection string
        env_vars = {
            'AZURE_COMPONENTS_DOWNLOAD_ENABLED': 'true',
        }

        with patch.dict(os.environ, env_vars, clear=True):
            from autolodge.blob_downloader import is_blob_download_enabled

            assert is_blob_download_enabled() is False

        # Test with blob download enabled and connection string
        env_vars = {
            'AZURE_COMPONENTS_DOWNLOAD_ENABLED': 'true',
            'AZURE_STORAGE_CONNECTION_STRING': 'test_connection_string',
        }

        with patch.dict(os.environ, env_vars, clear=True):
            from autolodge.blob_downloader import is_blob_download_enabled

            assert is_blob_download_enabled() is True
