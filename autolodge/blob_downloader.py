"""
Azure Blob Storage component downloader for ML inference service.

This module provides functionality to download all required processing components
(models, utility files, etc.) from Azure Blob Storage with comprehensive logging,
MD5 validation, and error handling. It integrates with the existing Azure Blob
Storage infrastructure and follows established patterns.
"""

import hashlib
import os
import time
from dataclasses import dataclass
from pathlib import Path
from typing import Dict, Optional, Tuple

from azure.storage.blob import BlobServiceClient
from dotenv import load_dotenv
from loguru import logger

# Load environment variables
load_dotenv()


@dataclass
class ComponentDownloadConfig:
    """
    Configuration class for Azure Blob Storage component downloads.

    This class loads and validates component download configuration from environment
    variables, providing proper error handling and validation.
    """

    # Azure Blob Storage connection details
    connection_string: Optional[str] = None
    container_name: str = 'autolodge-components'

    # Component blob paths (within container)
    model_blob_path: str = 'models/autolodge_20250605.h5'
    tokenizer_blob_path: str = 'utils/tk.pkl'
    label_encoder_blob_path: str = 'utils/le.pkl'
    abbreviations_blob_path: str = 'utils/abbr.csv'
    corpus_blob_path: str = 'utils/corpus.pkl'

    # Local destination directory
    local_dir: str = 'resources'

    # MD5 hashes for validation (optional)
    model_md5_hash: Optional[str] = None
    tokenizer_md5_hash: Optional[str] = None
    label_encoder_md5_hash: Optional[str] = None
    abbreviations_md5_hash: Optional[str] = None
    corpus_md5_hash: Optional[str] = None

    # Download control settings
    download_enabled: bool = False
    force_redownload: bool = False
    validate_checksums: bool = True

    def __post_init__(self):
        """Load configuration from environment variables after initialization."""
        self._load_from_environment()
        self._validate_configuration()

    def _load_from_environment(self) -> None:
        """Load component download configuration from environment variables."""
        logger.debug('📝 Loading Azure Blob Storage component configuration from environment variables...')

        # Connection details
        self.connection_string = os.getenv('AZURE_STORAGE_CONNECTION_STRING')
        self.container_name = os.getenv('AZURE_COMPONENTS_CONTAINER_NAME', self.container_name)

        # Component blob paths
        self.model_blob_path = os.getenv('AZURE_MODEL_BLOB_PATH', self.model_blob_path)
        self.tokenizer_blob_path = os.getenv('AZURE_TOKENIZER_BLOB_PATH', self.tokenizer_blob_path)
        self.label_encoder_blob_path = os.getenv('AZURE_LABEL_ENCODER_BLOB_PATH', self.label_encoder_blob_path)
        self.abbreviations_blob_path = os.getenv('AZURE_ABBREVIATIONS_BLOB_PATH', self.abbreviations_blob_path)
        self.corpus_blob_path = os.getenv('AZURE_CORPUS_BLOB_PATH', self.corpus_blob_path)

        # Local destination
        self.local_dir = os.getenv('AZURE_COMPONENTS_LOCAL_DIR', self.local_dir)

        # MD5 hashes
        self.model_md5_hash = os.getenv('AZURE_MODEL_MD5_HASH') or None
        self.tokenizer_md5_hash = os.getenv('AZURE_TOKENIZER_MD5_HASH') or None
        self.label_encoder_md5_hash = os.getenv('AZURE_LABEL_ENCODER_MD5_HASH') or None
        self.abbreviations_md5_hash = os.getenv('AZURE_ABBREVIATIONS_MD5_HASH') or None
        self.corpus_md5_hash = os.getenv('AZURE_CORPUS_MD5_HASH') or None

        # Download control
        self.download_enabled = os.getenv('AZURE_COMPONENTS_DOWNLOAD_ENABLED', 'false').lower() == 'true'
        self.force_redownload = os.getenv('AZURE_COMPONENTS_FORCE_REDOWNLOAD', 'false').lower() == 'true'
        self.validate_checksums = os.getenv('AZURE_COMPONENTS_VALIDATE_CHECKSUMS', 'true').lower() == 'true'

        logger.debug('✅ Azure Blob Storage component configuration loaded successfully')
        logger.debug(f'   📦 Container: {self.container_name}')
        logger.debug(f'   📁 Local directory: {self.local_dir}')
        logger.debug(f'   🔄 Download enabled: {self.download_enabled}')
        logger.debug(f'   🔄 Force redownload: {self.force_redownload}')
        logger.debug(f'   🔍 Validate checksums: {self.validate_checksums}')

    def _validate_configuration(self) -> None:
        """Validate the loaded configuration."""
        if self.download_enabled and not self.connection_string:
            raise ValueError(
                '❌ Azure Blob Storage connection string is required when component download is enabled. '
                'Please set AZURE_STORAGE_CONNECTION_STRING environment variable.'
            )

    def is_configured(self) -> bool:
        """Check if Azure Blob Storage component download is properly configured."""
        return self.download_enabled and bool(self.connection_string)

    def get_component_mapping(self) -> Dict[str, Tuple[str, str, Optional[str]]]:
        """
        Get mapping of component names to (blob_path, local_filename, md5_hash).

        Returns:
            Dict mapping component names to (blob_path, local_filename, md5_hash) tuples
        """
        return {
            'model': (self.model_blob_path, 'autolodge_20250605.h5', self.model_md5_hash),
            'tokenizer': (self.tokenizer_blob_path, 'tk.pkl', self.tokenizer_md5_hash),
            'label_encoder': (self.label_encoder_blob_path, 'le.pkl', self.label_encoder_md5_hash),
            'abbreviations': (self.abbreviations_blob_path, 'abbr.csv', self.abbreviations_md5_hash),
            'corpus': (self.corpus_blob_path, 'corpus.pkl', self.corpus_md5_hash),
        }


class AzureBlobComponentDownloader:
    """
    Azure Blob Storage component downloader with comprehensive logging and validation.

    This class handles downloading all required ML processing components from Azure Blob
    Storage with proper error handling, MD5 validation, and performance metrics.
    """

    def __init__(self, config: ComponentDownloadConfig):
        """
        Initialize the Azure Blob Storage component downloader.

        Args:
            config: Component download configuration
        """
        self.config = config
        self.blob_service_client: Optional[BlobServiceClient] = None
        self._initialize_blob_client()

    def _initialize_blob_client(self) -> None:
        """Initialize Azure Blob Service Client with error handling."""
        try:
            logger.info('🔐 Initializing Azure Blob Storage client for component downloads...')
            self.blob_service_client = BlobServiceClient.from_connection_string(self.config.connection_string)

            # Verify container exists
            try:
                container_client = self.blob_service_client.get_container_client(self.config.container_name)
                container_client.get_container_properties()
                logger.info(f'✅ Connected to Azure Blob Storage container: {self.config.container_name}')
            except Exception as e:
                logger.error(f'❌ Failed to access container {self.config.container_name}: {str(e)}')
                raise

        except Exception as e:
            logger.error(f'❌ Failed to initialize Azure Blob Storage client: {str(e)}')
            raise

    def _calculate_md5_hash(self, file_path: Path) -> str:
        """
        Calculate MD5 hash of a file using chunk-based reading for memory efficiency.

        Args:
            file_path: Path to the file

        Returns:
            MD5 hash as hexadecimal string
        """
        hash_md5 = hashlib.md5()
        with open(file_path, 'rb') as f:
            for chunk in iter(lambda: f.read(4096), b''):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()

    def _validate_component_checksum(self, file_path: Path, expected_hash: str, component_name: str) -> bool:
        """
        Validate component file integrity using MD5 hash.

        Args:
            file_path: Path to the downloaded file
            expected_hash: Expected MD5 hash
            component_name: Name of the component for logging

        Returns:
            True if validation passes, False otherwise
        """
        try:
            logger.info(f'🔍 Validating {component_name} integrity...')
            actual_hash = self._calculate_md5_hash(file_path)

            if actual_hash.lower() == expected_hash.lower():
                logger.info(f'✅ {component_name} integrity validation passed')
                logger.debug(f'   Expected: {expected_hash}')
                logger.debug(f'   Actual:   {actual_hash}')
                return True
            else:
                logger.error(f'❌ {component_name} integrity validation failed')
                logger.error(f'   Expected: {expected_hash}')
                logger.error(f'   Actual:   {actual_hash}')
                return False

        except Exception as e:
            logger.error(f'❌ Failed to validate {component_name} integrity: {str(e)}')
            return False

    def _download_component(
        self, component_name: str, blob_path: str, local_filename: str, expected_hash: Optional[str] = None
    ) -> bool:
        """
        Download a single component from Azure Blob Storage.

        Args:
            component_name: Name of the component for logging
            blob_path: Path to the blob in the container
            local_filename: Local filename to save the component
            expected_hash: Optional MD5 hash for validation

        Returns:
            True if download and validation successful, False otherwise
        """
        try:
            # Ensure local directory exists
            local_dir = Path(self.config.local_dir)
            local_dir.mkdir(parents=True, exist_ok=True)

            local_file_path = local_dir / local_filename

            # Check if file already exists and force redownload is not enabled
            if local_file_path.exists() and not self.config.force_redownload:
                logger.info(f'📁 {component_name} already exists locally: {local_file_path}')

                # Validate existing file if hash is provided
                if expected_hash and self.config.validate_checksums:
                    if self._validate_component_checksum(local_file_path, expected_hash, component_name):
                        logger.info(f'✅ Using existing {component_name} file')
                        return True
                    else:
                        logger.warning(f'⚠️ Existing {component_name} file failed validation, redownloading...')
                else:
                    logger.info(f'✅ Using existing {component_name} file (validation skipped)')
                    return True

            # Download the component
            logger.info(f'📥 Downloading {component_name} from Azure Blob Storage...')
            logger.debug(f'   Blob path: {blob_path}')
            logger.debug(f'   Local path: {local_file_path}')

            download_start_time = time.time()

            # Get blob client and download
            blob_client = self.blob_service_client.get_blob_client(container=self.config.container_name, blob=blob_path)

            with open(local_file_path, 'wb') as download_file:
                download_stream = blob_client.download_blob()
                download_file.write(download_stream.readall())

            download_duration = time.time() - download_start_time
            file_size = local_file_path.stat().st_size

            logger.info(f'✅ {component_name} downloaded successfully')
            logger.info(f'   📊 File size: {file_size / 1024:.2f} KB')
            logger.info(f'   ⏱️ Download time: {download_duration:.2f} seconds')
            logger.info(f'   🚀 Download speed: {(file_size / 1024) / download_duration:.2f} KB/s')

            # Validate downloaded file if hash is provided
            if expected_hash and self.config.validate_checksums:
                if not self._validate_component_checksum(local_file_path, expected_hash, component_name):
                    # Remove invalid file
                    local_file_path.unlink()
                    return False

            return True

        except Exception as e:
            logger.error(f'❌ Failed to download {component_name}: {str(e)}')
            return False

    def download_all_components(self) -> bool:
        """
        Download all required processing components from Azure Blob Storage.

        This method downloads all components defined in the configuration with
        comprehensive logging and error handling.

        Returns:
            True if all components downloaded successfully, False otherwise
        """
        try:
            logger.info('🚀 Starting download of all processing components from Azure Blob Storage...')
            download_start_time = time.time()

            component_mapping = self.config.get_component_mapping()
            successful_downloads = 0
            total_components = len(component_mapping)

            logger.info(f'📦 Components to download: {total_components}')

            for component_name, (blob_path, local_filename, expected_hash) in component_mapping.items():
                logger.info(f'📥 Processing component: {component_name}')

                if self._download_component(component_name, blob_path, local_filename, expected_hash):
                    successful_downloads += 1
                    logger.info(
                        f'✅ {component_name} processed successfully ({successful_downloads}/{total_components})'
                    )
                else:
                    logger.error(f'❌ Failed to process {component_name}')
                    return False

            total_duration = time.time() - download_start_time

            logger.info('🎉 All processing components downloaded successfully!')
            logger.info('📊 Download summary:')
            logger.info(f'   ✅ Successful: {successful_downloads}/{total_components}')
            logger.info(f'   ⏱️ Total time: {total_duration:.2f} seconds')
            logger.info(f'   📁 Local directory: {Path(self.config.local_dir).resolve()}')

            return True

        except Exception as e:
            logger.error(f'❌ Failed to download components: {str(e)}')
            return False

    def validate_all_components(self) -> bool:
        """
        Validate that all required components exist locally.

        Returns:
            True if all components exist and are valid, False otherwise
        """
        try:
            logger.info('🔍 Validating all required components...')

            component_mapping = self.config.get_component_mapping()
            local_dir = Path(self.config.local_dir)

            if not local_dir.exists():
                logger.error(f'❌ Local components directory does not exist: {local_dir}')
                return False

            missing_components = []
            invalid_components = []

            for component_name, (_, local_filename, expected_hash) in component_mapping.items():
                local_file_path = local_dir / local_filename

                if not local_file_path.exists():
                    missing_components.append(component_name)
                    logger.error(f'❌ Missing component: {component_name} ({local_file_path})')
                elif expected_hash and self.config.validate_checksums:
                    if not self._validate_component_checksum(local_file_path, expected_hash, component_name):
                        invalid_components.append(component_name)
                else:
                    logger.debug(f'✅ Component exists: {component_name}')

            if missing_components or invalid_components:
                logger.error('❌ Component validation failed:')
                if missing_components:
                    logger.error(f'   Missing: {", ".join(missing_components)}')
                if invalid_components:
                    logger.error(f'   Invalid: {", ".join(invalid_components)}')
                return False

            logger.info('✅ All required components validated successfully')
            return True

        except Exception as e:
            logger.error(f'❌ Component validation failed: {str(e)}')
            return False


def is_blob_download_enabled() -> bool:
    """
    Check if Azure Blob Storage component download is enabled.

    Returns:
        True if blob download is enabled and configured, False otherwise
    """
    try:
        config = ComponentDownloadConfig()
        return config.is_configured()
    except Exception as e:
        logger.debug(f'Blob download configuration check failed: {str(e)}')
        return False


def download_components_from_blob_storage() -> bool:
    """
    Download all required processing components from Azure Blob Storage.

    This is the main entry point for component downloads, providing a simple
    interface for the scoring service initialization.

    Returns:
        True if all components downloaded successfully, False otherwise
    """
    try:
        # Load configuration
        config = ComponentDownloadConfig()

        if not config.is_configured():
            logger.info('ℹ️ Azure Blob Storage component download is not enabled')
            return False

        # Create downloader and download components
        downloader = AzureBlobComponentDownloader(config)
        return downloader.download_all_components()

    except Exception as e:
        logger.error(f'❌ Component download from blob storage failed: {str(e)}')
        return False
