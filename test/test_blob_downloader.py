"""
Comprehensive unit tests for Azure Blob Storage component downloader.

This module tests the blob downloader functionality including configuration loading,
component downloading, MD5 validation, and error handling scenarios.
"""

import os
import tempfile
from pathlib import Path
from unittest.mock import MagicMock, Mock, mock_open, patch

import pytest

from autolodge.blob_downloader import (
    AzureBlobComponentDownloader,
    ComponentDownloadConfig,
    download_components_from_blob_storage,
    is_blob_download_enabled,
)


class TestComponentDownloadConfig:
    """Test cases for ComponentDownloadConfig class."""

    def test_config_default_values(self):
        """Test that ComponentDownloadConfig initializes with correct default values."""
        with patch.dict(os.environ, {}, clear=True):
            config = ComponentDownloadConfig()

            # Test default values
            assert config.container_name == 'autolodge-components'
            assert config.model_blob_path == 'models/autolodge_20250605.h5'
            assert config.tokenizer_blob_path == 'utils/tk.pkl'
            assert config.label_encoder_blob_path == 'utils/le.pkl'
            assert config.abbreviations_blob_path == 'utils/abbr.csv'
            assert config.corpus_blob_path == 'utils/corpus.pkl'
            assert config.local_dir == 'resources'
            assert config.download_enabled is False
            assert config.force_redownload is False
            assert config.validate_checksums is True

    def test_config_from_environment_full(self):
        """Test configuration loading with all environment variables set."""
        env_vars = {
            'AZURE_STORAGE_CONNECTION_STRING': 'test_connection_string',
            'AZURE_COMPONENTS_CONTAINER_NAME': 'test-container',
            'AZURE_MODEL_BLOB_PATH': 'models/test_model.h5',
            'AZURE_TOKENIZER_BLOB_PATH': 'utils/test_tokenizer.pkl',
            'AZURE_LABEL_ENCODER_BLOB_PATH': 'utils/test_encoder.pkl',
            'AZURE_ABBREVIATIONS_BLOB_PATH': 'utils/test_abbr.csv',
            'AZURE_CORPUS_BLOB_PATH': 'utils/test_corpus.pkl',
            'AZURE_COMPONENTS_LOCAL_DIR': 'test_resources',
            'AZURE_MODEL_MD5_HASH': 'test_model_hash',
            'AZURE_TOKENIZER_MD5_HASH': 'test_tokenizer_hash',
            'AZURE_LABEL_ENCODER_MD5_HASH': 'test_encoder_hash',
            'AZURE_ABBREVIATIONS_MD5_HASH': 'test_abbr_hash',
            'AZURE_CORPUS_MD5_HASH': 'test_corpus_hash',
            'AZURE_COMPONENTS_DOWNLOAD_ENABLED': 'true',
            'AZURE_COMPONENTS_FORCE_REDOWNLOAD': 'true',
            'AZURE_COMPONENTS_VALIDATE_CHECKSUMS': 'false',
        }

        with patch.dict(os.environ, env_vars, clear=True):
            config = ComponentDownloadConfig()

            assert config.connection_string == 'test_connection_string'
            assert config.container_name == 'test-container'
            assert config.model_blob_path == 'models/test_model.h5'
            assert config.tokenizer_blob_path == 'utils/test_tokenizer.pkl'
            assert config.label_encoder_blob_path == 'utils/test_encoder.pkl'
            assert config.abbreviations_blob_path == 'utils/test_abbr.csv'
            assert config.corpus_blob_path == 'utils/test_corpus.pkl'
            assert config.local_dir == 'test_resources'
            assert config.model_md5_hash == 'test_model_hash'
            assert config.tokenizer_md5_hash == 'test_tokenizer_hash'
            assert config.label_encoder_md5_hash == 'test_encoder_hash'
            assert config.abbreviations_md5_hash == 'test_abbr_hash'
            assert config.corpus_md5_hash == 'test_corpus_hash'
            assert config.download_enabled is True
            assert config.force_redownload is True
            assert config.validate_checksums is False

    def test_config_validation_missing_connection_string(self):
        """Test configuration validation when connection string is missing but download is enabled."""
        env_vars = {
            'AZURE_COMPONENTS_DOWNLOAD_ENABLED': 'true',
        }

        with patch.dict(os.environ, env_vars, clear=True):
            with pytest.raises(ValueError, match='Azure Blob Storage connection string is required'):
                ComponentDownloadConfig()

    def test_is_configured_true(self):
        """Test is_configured returns True when properly configured."""
        env_vars = {
            'AZURE_STORAGE_CONNECTION_STRING': 'test_connection_string',
            'AZURE_COMPONENTS_DOWNLOAD_ENABLED': 'true',
        }

        with patch.dict(os.environ, env_vars, clear=True):
            config = ComponentDownloadConfig()
            assert config.is_configured() is True

    def test_is_configured_false_disabled(self):
        """Test is_configured returns False when download is disabled."""
        env_vars = {
            'AZURE_STORAGE_CONNECTION_STRING': 'test_connection_string',
            'AZURE_COMPONENTS_DOWNLOAD_ENABLED': 'false',
        }

        with patch.dict(os.environ, env_vars, clear=True):
            config = ComponentDownloadConfig()
            assert config.is_configured() is False

    def test_is_configured_false_no_connection_string(self):
        """Test is_configured returns False when connection string is missing."""
        env_vars = {
            'AZURE_COMPONENTS_DOWNLOAD_ENABLED': 'false',  # Disabled, so no validation error
        }

        with patch.dict(os.environ, env_vars, clear=True):
            config = ComponentDownloadConfig()
            assert config.is_configured() is False

    def test_get_component_mapping(self):
        """Test get_component_mapping returns correct mapping."""
        env_vars = {
            'AZURE_STORAGE_CONNECTION_STRING': 'test_connection_string',
            'AZURE_COMPONENTS_DOWNLOAD_ENABLED': 'true',
            'AZURE_MODEL_MD5_HASH': 'model_hash',
            'AZURE_TOKENIZER_MD5_HASH': 'tokenizer_hash',
        }

        with patch.dict(os.environ, env_vars, clear=True):
            config = ComponentDownloadConfig()
            mapping = config.get_component_mapping()

            assert 'model' in mapping
            assert 'tokenizer' in mapping
            assert 'label_encoder' in mapping
            assert 'abbreviations' in mapping
            assert 'corpus' in mapping

            # Check model mapping
            model_blob_path, model_filename, model_hash = mapping['model']
            assert model_blob_path == 'models/autolodge_20250605.h5'
            assert model_filename == 'autolodge_20250605.h5'
            assert model_hash == 'model_hash'

            # Check tokenizer mapping
            tokenizer_blob_path, tokenizer_filename, tokenizer_hash = mapping['tokenizer']
            assert tokenizer_blob_path == 'utils/tk.pkl'
            assert tokenizer_filename == 'tk.pkl'
            assert tokenizer_hash == 'tokenizer_hash'


class TestAzureBlobComponentDownloader:
    """Test cases for AzureBlobComponentDownloader class."""

    @pytest.fixture
    def mock_config(self):
        """Create a mock configuration for testing."""
        config = Mock(spec=ComponentDownloadConfig)
        config.connection_string = 'test_connection_string'
        config.container_name = 'test-container'
        config.local_dir = 'test_resources'
        config.force_redownload = False
        config.validate_checksums = True
        config.get_component_mapping.return_value = {
            'model': ('models/test_model.h5', 'test_model.h5', 'test_hash'),
            'tokenizer': ('utils/test_tokenizer.pkl', 'test_tokenizer.pkl', None),
        }
        return config

    @patch('autolodge.blob_downloader.BlobServiceClient')
    def test_downloader_initialization_success(self, mock_blob_service_client, mock_config):
        """Test successful downloader initialization."""
        mock_client = MagicMock()
        mock_blob_service_client.from_connection_string.return_value = mock_client

        # Mock container client
        mock_container_client = MagicMock()
        mock_client.get_container_client.return_value = mock_container_client

        downloader = AzureBlobComponentDownloader(mock_config)

        assert downloader.config == mock_config
        assert downloader.blob_service_client == mock_client
        mock_blob_service_client.from_connection_string.assert_called_once_with('test_connection_string')

    @patch('autolodge.blob_downloader.BlobServiceClient')
    def test_downloader_initialization_failure(self, mock_blob_service_client, mock_config):
        """Test downloader initialization failure."""
        mock_blob_service_client.from_connection_string.side_effect = Exception('Connection failed')

        with pytest.raises(Exception, match='Connection failed'):
            AzureBlobComponentDownloader(mock_config)

    def test_calculate_md5_hash(self, mock_config):
        """Test MD5 hash calculation."""
        with patch('autolodge.blob_downloader.BlobServiceClient'):
            downloader = AzureBlobComponentDownloader(mock_config)

            # Create a temporary file with known content
            with tempfile.NamedTemporaryFile(delete=False) as temp_file:
                temp_file.write(b'test content')
                temp_file_path = Path(temp_file.name)

            try:
                # Calculate hash
                hash_result = downloader._calculate_md5_hash(temp_file_path)

                # Verify hash (MD5 of 'test content')
                expected_hash = '9473fdd0d880a43c21b7778d34872157'
                assert hash_result == expected_hash

            finally:
                # Clean up
                temp_file_path.unlink()

    @patch('autolodge.blob_downloader.BlobServiceClient')
    def test_validate_component_checksum_success(self, mock_blob_service_client, mock_config):
        """Test successful component checksum validation."""
        downloader = AzureBlobComponentDownloader(mock_config)

        # Create a temporary file with known content
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            temp_file.write(b'test content')
            temp_file_path = Path(temp_file.name)

        try:
            # Test validation with correct hash
            expected_hash = '9473fdd0d880a43c21b7778d34872157'  # MD5 of 'test content'
            result = downloader._validate_component_checksum(temp_file_path, expected_hash, 'test_component')

            assert result is True

        finally:
            # Clean up
            temp_file_path.unlink()

    @patch('autolodge.blob_downloader.BlobServiceClient')
    def test_validate_component_checksum_failure(self, mock_blob_service_client, mock_config):
        """Test component checksum validation failure."""
        downloader = AzureBlobComponentDownloader(mock_config)

        # Create a temporary file with known content
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            temp_file.write(b'test content')
            temp_file_path = Path(temp_file.name)

        try:
            # Test validation with incorrect hash
            wrong_hash = 'incorrect_hash'
            result = downloader._validate_component_checksum(temp_file_path, wrong_hash, 'test_component')

            assert result is False

        finally:
            # Clean up
            temp_file_path.unlink()


class TestUtilityFunctions:
    """Test cases for utility functions."""

    @patch('autolodge.blob_downloader.ComponentDownloadConfig')
    def test_is_blob_download_enabled_true(self, mock_config_class):
        """Test is_blob_download_enabled returns True when configured."""
        mock_config = Mock()
        mock_config.is_configured.return_value = True
        mock_config_class.return_value = mock_config

        result = is_blob_download_enabled()

        assert result is True

    @patch('autolodge.blob_downloader.ComponentDownloadConfig')
    def test_is_blob_download_enabled_false(self, mock_config_class):
        """Test is_blob_download_enabled returns False when not configured."""
        mock_config = Mock()
        mock_config.is_configured.return_value = False
        mock_config_class.return_value = mock_config

        result = is_blob_download_enabled()

        assert result is False

    @patch('autolodge.blob_downloader.ComponentDownloadConfig')
    def test_is_blob_download_enabled_exception(self, mock_config_class):
        """Test is_blob_download_enabled returns False on exception."""
        mock_config_class.side_effect = Exception('Configuration error')

        result = is_blob_download_enabled()

        assert result is False

    @patch('autolodge.blob_downloader.ComponentDownloadConfig')
    @patch('autolodge.blob_downloader.AzureBlobComponentDownloader')
    def test_download_components_from_blob_storage_success(self, mock_downloader_class, mock_config_class):
        """Test successful component download from blob storage."""
        # Setup mocks
        mock_config = Mock()
        mock_config.is_configured.return_value = True
        mock_config_class.return_value = mock_config

        mock_downloader = Mock()
        mock_downloader.download_all_components.return_value = True
        mock_downloader_class.return_value = mock_downloader

        result = download_components_from_blob_storage()

        assert result is True
        mock_downloader_class.assert_called_once_with(mock_config)
        mock_downloader.download_all_components.assert_called_once()

    @patch('autolodge.blob_downloader.ComponentDownloadConfig')
    def test_download_components_from_blob_storage_not_configured(self, mock_config_class):
        """Test component download when not configured."""
        mock_config = Mock()
        mock_config.is_configured.return_value = False
        mock_config_class.return_value = mock_config

        result = download_components_from_blob_storage()

        assert result is False

    @patch('autolodge.blob_downloader.ComponentDownloadConfig')
    @patch('autolodge.blob_downloader.AzureBlobComponentDownloader')
    def test_download_components_from_blob_storage_download_failure(self, mock_downloader_class, mock_config_class):
        """Test component download failure."""
        # Setup mocks
        mock_config = Mock()
        mock_config.is_configured.return_value = True
        mock_config_class.return_value = mock_config

        mock_downloader = Mock()
        mock_downloader.download_all_components.return_value = False
        mock_downloader_class.return_value = mock_downloader

        result = download_components_from_blob_storage()

        assert result is False

    @patch('autolodge.blob_downloader.ComponentDownloadConfig')
    def test_download_components_from_blob_storage_exception(self, mock_config_class):
        """Test component download with exception."""
        mock_config_class.side_effect = Exception('Configuration error')

        result = download_components_from_blob_storage()

        assert result is False


class TestIntegrationScenarios:
    """Test cases for integration scenarios."""

    @patch('autolodge.blob_downloader.BlobServiceClient')
    def test_end_to_end_download_scenario(self, mock_blob_service_client):
        """Test complete end-to-end download scenario."""
        # Setup environment
        env_vars = {
            'AZURE_STORAGE_CONNECTION_STRING': 'test_connection_string',
            'AZURE_COMPONENTS_DOWNLOAD_ENABLED': 'true',
            'AZURE_COMPONENTS_CONTAINER_NAME': 'test-container',
            'AZURE_MODEL_MD5_HASH': '9473fdd0d880a43c21b7778d34872157',
        }

        with patch.dict(os.environ, env_vars, clear=True):
            # Setup mocks
            mock_client = MagicMock()
            mock_blob_service_client.from_connection_string.return_value = mock_client

            mock_container_client = MagicMock()
            mock_client.get_container_client.return_value = mock_container_client

            mock_blob_client = MagicMock()
            mock_client.get_blob_client.return_value = mock_blob_client
            mock_download_stream = MagicMock()
            mock_download_stream.readall.return_value = b'test content'
            mock_blob_client.download_blob.return_value = mock_download_stream

            # Mock file system operations
            with patch('autolodge.blob_downloader.Path') as mock_path_class:
                mock_local_dir = MagicMock()
                mock_local_file = MagicMock()
                mock_local_file.exists.return_value = False
                mock_local_file.stat.return_value.st_size = 1024
                mock_local_dir.__truediv__.return_value = mock_local_file
                mock_path_class.return_value = mock_local_dir

                # Mock the file operations and MD5 validation
                with patch('builtins.open', mock_open()):
                    # Mock the MD5 validation to always pass since we're not testing file integrity here
                    with patch(
                        'autolodge.blob_downloader.AzureBlobComponentDownloader._validate_component_checksum',
                        return_value=True,
                    ):
                        result = download_components_from_blob_storage()

            assert result is True
